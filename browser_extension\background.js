/**
 * ScreenMonitorMCP Browser Extension - Background Service Worker
 * 
 * <PERSON>u dosya browser extension'ın background service worker'ıdır.
 * MCP server ile iletişimi yönetir ve domain whitelist kontrolü yapar.
 */

class MCPExtensionBackground {
    constructor() {
        this.mcpServerUrl = 'http://localhost:7777';
        this.sessionId = this.generateSessionId();
        this.isConnected = false;
        this.allowedDomains = [];
        this.activeTab = null;
        
        this.init();
    }
    
    async init() {
        console.log('ScreenMonitorMCP Extension Background initialized');
        
        // Extension install/startup
        chrome.runtime.onInstalled.addListener(() => {
            this.loadConfiguration();
        });
        
        // Tab activation
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.handleTabActivation(activeInfo.tabId);
        });
        
        // Tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tab);
            }
        });
        
        // Message handling from content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
    }
    
    generateSessionId() {
        return 'ext_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async loadConfiguration() {
        try {
            // Load configuration from storage
            const config = await chrome.storage.local.get([
                'mcpServerUrl', 
                'allowedDomains', 
                'extensionEnabled'
            ]);
            
            this.mcpServerUrl = config.mcpServerUrl || 'http://localhost:7777';
            this.allowedDomains = config.allowedDomains || ['localhost', '127.0.0.1', 'github.com'];
            this.extensionEnabled = config.extensionEnabled !== false;

            // Force save default configuration if not exists
            if (!config.allowedDomains) {
                await chrome.storage.local.set({
                    allowedDomains: ['localhost', '127.0.0.1', 'github.com'],
                    extensionEnabled: true,
                    mcpServerUrl: 'http://localhost:7777'
                });
                console.log('Default configuration saved');
            }
            
            console.log('Configuration loaded:', {
                serverUrl: this.mcpServerUrl,
                domains: this.allowedDomains,
                enabled: this.extensionEnabled
            });
            
        } catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }
    
    async handleTabActivation(tabId) {
        try {
            const tab = await chrome.tabs.get(tabId);
            this.activeTab = tab;
            
            if (this.isDomainAllowed(tab.url)) {
                await this.registerWithMCP(tab);
            }
        } catch (error) {
            console.error('Tab activation error:', error);
        }
    }
    
    async handleTabUpdate(tab) {
        if (this.isDomainAllowed(tab.url)) {
            await this.registerWithMCP(tab);
        }
    }
    
    isDomainAllowed(url) {
        if (!url) return false;

        try {
            const urlObj = new URL(url);

            // Special handling for file:// protocol (test pages)
            if (urlObj.protocol === 'file:') {
                return this.allowedDomains.includes('localhost') ||
                       this.allowedDomains.includes('file');
            }

            const domain = urlObj.hostname;
            return this.allowedDomains.some(allowed =>
                domain === allowed || domain.endsWith('.' + allowed)
            );
        } catch (error) {
            console.error('URL parsing error:', error);
            return false;
        }
    }
    
    async registerWithMCP(tab) {
        if (!this.extensionEnabled) return;

        try {
            const urlObj = new URL(tab.url);
            let domain = urlObj.hostname;

            // Special handling for file:// protocol
            if (urlObj.protocol === 'file:') {
                domain = 'localhost'; // Treat file:// as localhost for MCP
            }

            const features = ['dom_monitoring', 'smart_click', 'text_extraction'];
            
            const response = await fetch(`${this.mcpServerUrl}/api/extension/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    domain: domain,
                    features: features,
                    user_agent: navigator.userAgent,
                    session_id: this.sessionId,
                    tab_id: tab.id,
                    url: tab.url
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                this.isConnected = result.success;
                
                console.log('MCP registration result:', result);
                
                // Notify content script
                chrome.tabs.sendMessage(tab.id, {
                    type: 'MCP_REGISTERED',
                    sessionId: this.sessionId,
                    features: features,
                    success: result.success
                });
            }
            
        } catch (error) {
            console.error('MCP registration failed:', error);
            this.isConnected = false;
        }
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.type) {
                case 'DOM_EVENT':
                    await this.forwardDOMEvent(message.data, sender.tab);
                    sendResponse({ success: true });
                    break;
                    
                case 'SMART_CLICK_REQUEST':
                    const clickResult = await this.handleSmartClick(message.data, sender.tab);
                    sendResponse(clickResult);
                    break;
                    
                case 'GET_CONFIG':
                    sendResponse({
                        sessionId: this.sessionId,
                        isConnected: this.isConnected,
                        allowedDomains: this.allowedDomains,
                        mcpServerUrl: this.mcpServerUrl
                    });
                    break;
                    
                default:
                    sendResponse({ error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('Message handling error:', error);
            sendResponse({ error: error.message });
        }
    }
    
    async forwardDOMEvent(eventData, tab) {
        if (!this.isConnected) return;
        
        try {
            await fetch(`${this.mcpServerUrl}/api/extension/dom-event`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    event_type: eventData.type,
                    event_data: eventData,
                    timestamp: new Date().toISOString(),
                    tab_id: tab.id,
                    url: tab.url
                })
            });
        } catch (error) {
            console.error('Failed to forward DOM event:', error);
        }
    }
    
    async handleSmartClick(clickData, tab) {
        if (!this.isConnected) return { success: false, error: 'Not connected to MCP' };
        
        try {
            const response = await fetch(`${this.mcpServerUrl}/api/extension/smart-click`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    element_description: clickData.description,
                    css_selector: clickData.selector,
                    confidence_threshold: clickData.confidence || 0.8,
                    tab_id: tab.id,
                    url: tab.url
                })
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                return { success: false, error: 'MCP server error' };
            }
            
        } catch (error) {
            console.error('Smart click request failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Initialize background service worker
const mcpExtension = new MCPExtensionBackground();
