/**
 * ScreenMonitorMCP Browser Extension - Content Script
 * 
 * Bu script web sayfalarında çalışır ve DOM değişikliklerini izler.
 * MCP server ile background script üzerinden iletişim kurar.
 */

class ScreenMonitorContentScript {
    constructor() {
        this.sessionId = null;
        this.isConnected = false;
        this.observer = null;
        this.config = null;
        this.eventQueue = [];
        this.isProcessing = false;
        
        this.init();
    }
    
    async init() {
        console.log('ScreenMonitorMCP Content Script initialized on:', window.location.hostname);
        
        // Background script'ten config al
        await this.getConfig();
        
        // MCP registration'ı bekle
        this.waitForRegistration();
        
        // Message listener
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        
        // Page load complete'i bekle
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startMonitoring());
        } else {
            this.startMonitoring();
        }
    }
    
    async getConfig() {
        try {
            const response = await chrome.runtime.sendMessage({ type: 'GET_CONFIG' });
            this.config = response;
            console.log('Content script config:', this.config);
        } catch (error) {
            console.error('Failed to get config:', error);
        }
    }
    
    waitForRegistration() {
        // Background script'in MCP registration'ını bekle
        setTimeout(() => {
            if (!this.isConnected) {
                console.log('Waiting for MCP registration...');
                this.waitForRegistration();
            }
        }, 1000);
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'MCP_REGISTERED':
                this.sessionId = message.sessionId;
                this.isConnected = message.success;
                console.log('MCP registration received:', message);
                if (this.isConnected) {
                    this.startMonitoring();
                }
                sendResponse({ received: true });
                break;
                
            case 'SMART_CLICK_EXECUTE':
                this.executeSmartClick(message.data);
                sendResponse({ executed: true });
                break;
                
            default:
                sendResponse({ error: 'Unknown message type' });
        }
    }
    
    startMonitoring() {
        if (!this.isConnected || this.observer) {
            return;
        }
        
        console.log('Starting DOM monitoring...');
        
        // MutationObserver setup
        this.observer = new MutationObserver((mutations) => {
            this.handleMutations(mutations);
        });
        
        // Monitoring configuration
        const observerConfig = {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true,
            characterData: true,
            characterDataOldValue: true
        };
        
        // Start observing
        this.observer.observe(document.body, observerConfig);
        
        // Initial page analysis
        this.analyzeInitialPage();
        
        // Event listeners
        this.setupEventListeners();
        
        console.log('DOM monitoring started successfully');
    }
    
    handleMutations(mutations) {
        const significantMutations = mutations.filter(mutation => 
            this.isSignificantMutation(mutation)
        );
        
        if (significantMutations.length > 0) {
            const event = {
                type: 'dom_change',
                timestamp: new Date().toISOString(),
                url: window.location.href,
                mutations: significantMutations.map(mutation => ({
                    type: mutation.type,
                    target: this.getElementInfo(mutation.target),
                    addedNodes: mutation.addedNodes.length,
                    removedNodes: mutation.removedNodes.length,
                    attributeName: mutation.attributeName,
                    oldValue: mutation.oldValue
                }))
            };
            
            this.queueEvent(event);
        }
    }
    
    isSignificantMutation(mutation) {
        // Filter out insignificant changes
        if (mutation.type === 'attributes') {
            // Ignore style changes, focus, etc.
            const ignoredAttributes = ['style', 'class', 'data-focus', 'aria-selected'];
            return !ignoredAttributes.includes(mutation.attributeName);
        }
        
        if (mutation.type === 'childList') {
            // Ignore text node changes
            return mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0;
        }
        
        return true;
    }
    
    getElementInfo(element) {
        if (!element || element.nodeType !== Node.ELEMENT_NODE) {
            return { tagName: 'TEXT_NODE' };
        }
        
        return {
            tagName: element.tagName,
            id: element.id,
            className: element.className,
            textContent: element.textContent?.substring(0, 100),
            selector: this.generateSelector(element)
        };
    }
    
    generateSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.length > 0);
            if (classes.length > 0) {
                return `${element.tagName.toLowerCase()}.${classes[0]}`;
            }
        }
        
        return element.tagName.toLowerCase();
    }
    
    setupEventListeners() {
        // Click events
        document.addEventListener('click', (event) => {
            this.queueEvent({
                type: 'click',
                timestamp: new Date().toISOString(),
                url: window.location.href,
                target: this.getElementInfo(event.target),
                coordinates: { x: event.clientX, y: event.clientY }
            });
        });
        
        // Form submissions
        document.addEventListener('submit', (event) => {
            this.queueEvent({
                type: 'form_submit',
                timestamp: new Date().toISOString(),
                url: window.location.href,
                target: this.getElementInfo(event.target)
            });
        });
        
        // Navigation changes (for SPAs)
        let lastUrl = window.location.href;
        new MutationObserver(() => {
            const currentUrl = window.location.href;
            if (currentUrl !== lastUrl) {
                lastUrl = currentUrl;
                this.queueEvent({
                    type: 'navigation_change',
                    timestamp: new Date().toISOString(),
                    url: currentUrl,
                    previousUrl: lastUrl
                });
            }
        }).observe(document, { subtree: true, childList: true });
    }
    
    analyzeInitialPage() {
        const pageInfo = {
            type: 'page_load',
            timestamp: new Date().toISOString(),
            url: window.location.href,
            title: document.title,
            elementCounts: {
                buttons: document.querySelectorAll('button').length,
                links: document.querySelectorAll('a').length,
                forms: document.querySelectorAll('form').length,
                inputs: document.querySelectorAll('input').length
            }
        };
        
        this.queueEvent(pageInfo);
    }
    
    queueEvent(event) {
        this.eventQueue.push(event);
        
        if (!this.isProcessing) {
            this.processEventQueue();
        }
    }
    
    async processEventQueue() {
        if (this.isProcessing || this.eventQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.eventQueue.length > 0) {
            const event = this.eventQueue.shift();
            
            try {
                await chrome.runtime.sendMessage({
                    type: 'DOM_EVENT',
                    data: event
                });
            } catch (error) {
                console.error('Failed to send DOM event:', error);
            }
            
            // Rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        this.isProcessing = false;
    }
    
    async executeSmartClick(clickData) {
        const { description, selector, confidence } = clickData;
        
        let element = null;
        
        // Try CSS selector first
        if (selector) {
            element = document.querySelector(selector);
        }
        
        // Fallback to text-based search
        if (!element) {
            element = this.findElementByDescription(description);
        }
        
        if (element) {
            // Scroll into view
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Highlight element
            this.highlightElement(element);
            
            // Click after a short delay
            setTimeout(() => {
                element.click();
                console.log('Smart click executed on:', element);
            }, 500);
            
            return { success: true, element: this.getElementInfo(element) };
        } else {
            console.warn('Element not found for smart click:', description);
            return { success: false, error: 'Element not found' };
        }
    }
    
    findElementByDescription(description) {
        const searchText = description.toLowerCase();
        
        // Search buttons
        const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]'));
        for (const button of buttons) {
            if (button.textContent.toLowerCase().includes(searchText) ||
                button.value?.toLowerCase().includes(searchText)) {
                return button;
            }
        }
        
        // Search links
        const links = Array.from(document.querySelectorAll('a'));
        for (const link of links) {
            if (link.textContent.toLowerCase().includes(searchText)) {
                return link;
            }
        }
        
        // Search by aria-label
        const ariaElements = Array.from(document.querySelectorAll('[aria-label]'));
        for (const element of ariaElements) {
            if (element.getAttribute('aria-label').toLowerCase().includes(searchText)) {
                return element;
            }
        }
        
        return null;
    }
    
    highlightElement(element) {
        const originalStyle = element.style.cssText;
        element.style.cssText += 'border: 3px solid #ff6b6b !important; background-color: rgba(255, 107, 107, 0.1) !important;';
        
        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, 2000);
    }
}

// Initialize content script
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new ScreenMonitorContentScript();
    });
} else {
    new ScreenMonitorContentScript();
}
