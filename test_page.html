<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScreenMonitorMCP Extension Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section h3 {
            margin-top: 0;
            color: #2d3748;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
        }
        
        .btn-success:hover {
            background: #38a169;
        }
        
        .btn-warning {
            background: #ed8936;
            color: white;
        }
        
        .btn-warning:hover {
            background: #dd6b20;
        }
        
        .btn-danger {
            background: #f56565;
            color: white;
        }
        
        .btn-danger:hover {
            background: #e53e3e;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2d3748;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #cbd5e0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .dynamic-content {
            min-height: 100px;
            padding: 20px;
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            text-align: center;
            color: #718096;
        }
        
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected {
            background: #48bb78;
        }
        
        .status-disconnected {
            background: #f56565;
        }
        
        .highlight {
            background: rgba(255, 107, 107, 0.2);
            border: 2px solid #ff6b6b;
            transition: all 0.3s;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 ScreenMonitorMCP Extension Test</h1>
        <p>Bu sayfa browser extension'ın DOM monitoring ve smart click özelliklerini test etmek için tasarlanmıştır.</p>
        <div id="extension-status">
            <span class="status-indicator status-disconnected"></span>
            Extension Status: <span id="status-text">Checking...</span>
        </div>
    </div>
    
    <div class="section">
        <h3>🎯 Smart Click Test</h3>
        <p>Bu butonlar AI assistant'ın "smart click" özelliğini test etmek için kullanılır.</p>
        
        <div class="button-group">
            <button id="save-btn" class="btn-primary">Save Document</button>
            <button id="submit-btn" class="btn-success">Submit Form</button>
            <button id="cancel-btn" class="btn-warning">Cancel Action</button>
            <button id="delete-btn" class="btn-danger">Delete Item</button>
        </div>
        
        <p><strong>Test komutu:</strong> "Click the save button" veya "Submit form butonuna tıkla"</p>
    </div>
    
    <div class="section">
        <h3>📝 Form Interaction Test</h3>
        <p>Form etkileşimlerini test etmek için:</p>
        
        <form id="test-form">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" placeholder="Enter your username">
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" placeholder="Enter your email">
            </div>
            
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" rows="4" placeholder="Enter your message"></textarea>
            </div>
            
            <div class="form-group">
                <label for="category">Category:</label>
                <select id="category" name="category">
                    <option value="">Select category</option>
                    <option value="bug">Bug Report</option>
                    <option value="feature">Feature Request</option>
                    <option value="question">Question</option>
                </select>
            </div>
            
            <button type="submit" class="btn-primary">Submit Form</button>
        </form>
    </div>
    
    <div class="section">
        <h3>🔄 Dynamic Content Test</h3>
        <p>DOM değişikliklerini test etmek için:</p>
        
        <div class="button-group">
            <button onclick="addContent()" class="btn-primary">Add Content</button>
            <button onclick="removeContent()" class="btn-warning">Remove Content</button>
            <button onclick="changeContent()" class="btn-success">Change Content</button>
        </div>
        
        <div id="dynamic-area" class="dynamic-content">
            Dynamic content will appear here...
        </div>
    </div>
    
    <div class="section">
        <h3>📊 Extension Activity Log</h3>
        <p>Extension'dan gelen event'ler burada görünecek:</p>
        
        <button onclick="clearLog()" class="btn-warning">Clear Log</button>
        <div id="activity-log" class="log">
            Extension activity will be logged here...
        </div>
    </div>
    
    <script>
        // Test page functionality
        let contentCounter = 0;
        
        function addContent() {
            contentCounter++;
            const area = document.getElementById('dynamic-area');
            const newElement = document.createElement('div');
            newElement.innerHTML = `<p>Dynamic content #${contentCounter} - ${new Date().toLocaleTimeString()}</p>`;
            newElement.style.padding = '10px';
            newElement.style.margin = '5px 0';
            newElement.style.background = '#e6fffa';
            newElement.style.border = '1px solid #81e6d9';
            newElement.style.borderRadius = '4px';
            area.appendChild(newElement);
            
            logActivity(`Added dynamic content #${contentCounter}`);
        }
        
        function removeContent() {
            const area = document.getElementById('dynamic-area');
            const lastChild = area.lastElementChild;
            if (lastChild && lastChild.tagName !== 'P') {
                area.removeChild(lastChild);
                logActivity('Removed last dynamic content');
            }
        }
        
        function changeContent() {
            const area = document.getElementById('dynamic-area');
            if (area.children.length > 1) {
                const randomChild = area.children[Math.floor(Math.random() * (area.children.length - 1)) + 1];
                randomChild.style.background = `hsl(${Math.random() * 360}, 70%, 90%)`;
                randomChild.innerHTML = `<p>Modified content - ${new Date().toLocaleTimeString()}</p>`;
                logActivity('Modified random content');
            }
        }
        
        function logActivity(message) {
            const log = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('activity-log').innerHTML = 'Log cleared...\n';
        }
        
        // Button click handlers
        document.getElementById('save-btn').addEventListener('click', () => {
            logActivity('Save button clicked!');
            alert('Save button clicked! Extension should detect this.');
        });
        
        document.getElementById('submit-btn').addEventListener('click', () => {
            logActivity('Submit button clicked!');
            alert('Submit button clicked! Extension should detect this.');
        });
        
        document.getElementById('cancel-btn').addEventListener('click', () => {
            logActivity('Cancel button clicked!');
            alert('Cancel button clicked! Extension should detect this.');
        });
        
        document.getElementById('delete-btn').addEventListener('click', () => {
            logActivity('Delete button clicked!');
            if (confirm('Are you sure you want to delete?')) {
                alert('Delete confirmed! Extension should detect this.');
            }
        });
        
        // Form submission handler
        document.getElementById('test-form').addEventListener('submit', (e) => {
            e.preventDefault();
            logActivity('Form submitted!');
            alert('Form submitted! Extension should detect this.');
        });
        
        // Check for extension
        function checkExtensionStatus() {
            // This would be updated by the extension if it's running
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('status-text');
            
            // Simulate extension detection (in real scenario, extension would update this)
            setTimeout(() => {
                if (window.screenMonitorExtension) {
                    statusIndicator.className = 'status-indicator status-connected';
                    statusText.textContent = 'Connected';
                    logActivity('ScreenMonitorMCP Extension detected and connected!');
                } else {
                    statusIndicator.className = 'status-indicator status-disconnected';
                    statusText.textContent = 'Not detected';
                    logActivity('ScreenMonitorMCP Extension not detected. Please install and enable the extension.');
                }
            }, 1000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            logActivity('Test page loaded');
            checkExtensionStatus();
        });
        
        // Listen for extension messages (if extension is present)
        window.addEventListener('message', (event) => {
            if (event.data.type === 'SCREEN_MONITOR_EXTENSION') {
                logActivity(`Extension message: ${JSON.stringify(event.data)}`);
            }
        });
    </script>
</body>
</html>
