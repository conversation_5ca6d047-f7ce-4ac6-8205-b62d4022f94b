/**
 * ScreenMonitorMCP Browser Extension - Popup Script
 */

class PopupController {
    constructor() {
        this.config = null;
        this.stats = { events: 0, sessionStart: Date.now() };
        this.updateInterval = null;
        
        this.init();
    }
    
    async init() {
        await this.loadConfig();
        this.setupEventListeners();
        this.updateUI();
        this.startStatsUpdate();
    }
    
    async loadConfig() {
        try {
            this.config = await chrome.runtime.sendMessage({ type: 'GET_CONFIG' });
            console.log('Popup config loaded:', this.config);
        } catch (error) {
            console.error('Failed to load config:', error);
            this.config = {
                isConnected: false,
                allowedDomains: ['localhost'],
                sessionId: null
            };
        }
    }
    
    setupEventListeners() {
        // Toggle monitoring button
        document.getElementById('toggle-monitoring').addEventListener('click', () => {
            this.toggleMonitoring();
        });
        
        // Settings button
        document.getElementById('open-options').addEventListener('click', () => {
            chrome.runtime.openOptionsPage();
        });
    }
    
    updateUI() {
        this.updateStatus();
        this.updateDomainList();
        this.updateFeatureList();
        this.updateCurrentDomain();
    }
    
    updateStatus() {
        const statusElement = document.getElementById('status');
        const statusText = document.getElementById('status-text');
        
        if (this.config.isConnected) {
            statusElement.className = 'status status-connected';
            statusText.textContent = 'Connected to MCP';
        } else {
            statusElement.className = 'status status-disconnected';
            statusText.textContent = 'Disconnected';
        }
    }
    
    async updateCurrentDomain() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const domain = new URL(tab.url).hostname;
            
            document.getElementById('current-domain').textContent = domain;
            
            // Check if current domain is allowed
            const isAllowed = this.config.allowedDomains.includes(domain) || 
                             this.config.allowedDomains.includes('localhost');
            
            const domainList = document.getElementById('domain-list');
            domainList.innerHTML = `
                <div class="domain-item">
                    <span class="domain-name">${domain}</span>
                    <span class="domain-status ${isAllowed ? 'domain-active' : 'domain-inactive'}">
                        ${isAllowed ? 'Active' : 'Blocked'}
                    </span>
                </div>
            `;
        } catch (error) {
            console.error('Failed to get current domain:', error);
        }
    }
    
    updateDomainList() {
        const domainList = document.getElementById('domain-list');
        
        if (!this.config.allowedDomains || this.config.allowedDomains.length === 0) {
            domainList.innerHTML = '<div style="text-align: center; color: #718096; padding: 20px;">No domains configured</div>';
            return;
        }
        
        // This will be updated by updateCurrentDomain()
    }
    
    updateFeatureList() {
        const featureList = document.getElementById('feature-list');
        
        const features = [
            { name: 'DOM Monitoring', key: 'dom_monitoring', enabled: true },
            { name: 'Smart Click', key: 'smart_click', enabled: true },
            { name: 'Text Extraction', key: 'text_extraction', enabled: true },
            { name: 'Event Analysis', key: 'event_analysis', enabled: this.config.isConnected }
        ];
        
        featureList.innerHTML = features.map(feature => `
            <div class="feature-item">
                <div class="feature-icon ${feature.enabled ? 'feature-enabled' : 'feature-disabled'}"></div>
                <span class="feature-name">${feature.name}</span>
            </div>
        `).join('');
    }
    
    startStatsUpdate() {
        this.updateStats();
        this.updateInterval = setInterval(() => {
            this.updateStats();
        }, 1000);
    }
    
    updateStats() {
        // Update session time
        const sessionTime = Math.floor((Date.now() - this.stats.sessionStart) / 1000 / 60);
        document.getElementById('session-time').textContent = `${sessionTime}m`;
        
        // Update events count (this would come from background script in real implementation)
        document.getElementById('events-count').textContent = this.stats.events;
    }
    
    async toggleMonitoring() {
        const button = document.getElementById('toggle-monitoring');
        
        if (this.config.isConnected) {
            // Stop monitoring
            button.textContent = 'Start Monitoring';
            button.className = 'button button-primary';
            this.config.isConnected = false;
        } else {
            // Start monitoring
            try {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                const domain = new URL(tab.url).hostname;
                
                // Check if domain is allowed
                const isAllowed = this.config.allowedDomains.includes(domain) || 
                                 this.config.allowedDomains.includes('localhost');
                
                if (!isAllowed) {
                    alert(`Domain "${domain}" is not in the whitelist. Please add it in settings.`);
                    return;
                }
                
                // Send start monitoring message
                await chrome.runtime.sendMessage({
                    type: 'START_MONITORING',
                    domain: domain
                });
                
                button.textContent = 'Stop Monitoring';
                button.className = 'button button-secondary';
                this.config.isConnected = true;
                
            } catch (error) {
                console.error('Failed to start monitoring:', error);
                alert('Failed to start monitoring. Please check MCP server connection.');
            }
        }
        
        this.updateUI();
    }
    
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});

// Cleanup when popup is closed
window.addEventListener('beforeunload', () => {
    if (window.popupController) {
        window.popupController.destroy();
    }
});
