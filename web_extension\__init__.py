"""
ScreenMonitorMCP Web Extension Module

Bu modül mevcut ScreenMonitorMCP sistemine browser extension deste<PERSON><PERSON> ekler.
Mevcut core functionality'yi et<PERSON><PERSON>ez ve tamamen opsiyoneldir.
"""

__version__ = "1.0.0"
__author__ = "ScreenMonitorMCP Team"

# Extension feature flags
EXTENSION_FEATURES = {
    "web_monitoring": True,
    "dom_detection": True,
    "web_smart_click": True,
    "domain_whitelist": True,
    "web_automation": True
}

def is_extension_enabled() -> bool:
    """Check if web extension features are enabled"""
    import os
    return os.getenv("ENABLE_WEB_EXTENSION", "false").lower() == "true"

def get_enabled_features() -> dict:
    """Get currently enabled extension features"""
    if not is_extension_enabled():
        return {}
    return {k: v for k, v in EXTENSION_FEATURES.items() if v}

def get_allowed_domains() -> list:
    """Get allowed domains from environment"""
    import os
    domains_str = os.getenv("ALLOWED_DOMAINS", "localhost")
    return [domain.strip() for domain in domains_str.split(",") if domain.strip()]

def is_domain_allowed(domain: str) -> bool:
    """Check if domain is in whitelist"""
    allowed = get_allowed_domains()
    return domain in allowed or "localhost" in allowed

def get_extension_config() -> dict:
    """Get complete extension configuration"""
    import os
    return {
        "enabled": is_extension_enabled(),
        "features": get_enabled_features(),
        "allowed_domains": get_allowed_domains(),
        "api_key": os.getenv("WEB_EXTENSION_API_KEY", ""),
        "version": __version__
    }
