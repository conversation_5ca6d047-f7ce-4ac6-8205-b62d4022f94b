# ========================================
# ScreenMonitorMCP Environment Variables Configuration
# ========================================
# Bu dosyayı '.env' olarak yeniden adlandırın ve gerçek API anahtarlarınızı girin.
# Rename this file to '.env' and fill in your actual API keys for usage.

# ========================================
# SERVER CONFIGURATION / SUNUCU YAPILANDIRMASI
# ========================================

# Server API Key for securing endpoints / Endpoint güvenliği için sunucu API anahtarı
API_KEY=your_secret_server_key_here

# Server host and port configuration / Sunucu host ve port yapılandırması
HOST=127.0.0.1
PORT=7777

# ========================================
# AI MODEL CONFIGURATION / AI MODEL YAPILANDIRMASI
# ========================================

# OpenAI API Key for vision analysis / Görüntü analizi için OpenAI API anahtarı
OPENAI_API_KEY=sk-your-openai-api-key-here

# Custom OpenAI API Base URL / Özel OpenAI API Base URL
# Default: https://api.openai.com/v1 (OpenAI resmi endpoint)
# Alternative: https://openrouter.ai/api/v1 (OpenRouter için)
OPENAI_BASE_URL=https://openrouter.ai/api/v1

# Default AI Model for Analysis / Analiz için varsayılan AI modeli
# Önerilen modeller / Recommended models:
# - gpt-4o-mini (OpenAI - hızlı ve ekonomik)
# - gpt-4o (OpenAI - en gelişmiş)
# - mistralai/mistral-small-3.2-24b-instruct:free (OpenRouter - ücretsiz)
# - anthropic/claude-3-haiku (OpenRouter - hızlı)
DEFAULT_OPENAI_MODEL=mistralai/mistral-small-3.2-24b-instruct:free

# Default Max Tokens for AI Analysis / AI analizi için varsayılan maksimum token sayısı
# Minimum 1000 önerilir - AI modellerin esnek yanıt verebilmesi için
# Recommended minimum 1000 - for flexible AI model responses
DEFAULT_MAX_TOKENS=1000

# ========================================
# MONITORING CONFIGURATION / İZLEME YAPILANDIRMASI
# ========================================

# Continuous monitoring settings / Sürekli izleme ayarları
DEFAULT_FPS=2
DEFAULT_CHANGE_THRESHOLD=0.1
DEFAULT_MAJOR_CHANGE_THRESHOLD=0.3
DEFAULT_CRITICAL_CHANGE_THRESHOLD=0.6

# Screenshot storage / Ekran görüntüsü depolama
SAVE_SCREENSHOTS=true
SCREENSHOT_DIR=./screenshots

# ========================================
# ADVANCED FEATURES / GELİŞMİŞ ÖZELLİKLER
# ========================================

# Smart detection features / Akıllı algılama özellikleri
SMART_DETECTION=true
UI_ELEMENT_DETECTION=true
OCR_ENGINE=auto

# User behavior learning / Kullanıcı davranış öğrenme
ENABLE_USER_LEARNING=true
PREDICTION_HORIZON=30

# ========================================
# WEB EXTENSION FEATURES / WEB UZANTI ÖZELLİKLERİ
# ========================================

# Enable browser extension support / Browser uzantı desteğini etkinleştir
# Set to 'true' to enable web extension features
ENABLE_WEB_EXTENSION=false

# Web extension security settings / Web uzantı güvenlik ayarları
WEB_EXTENSION_API_KEY=your_web_extension_api_key_here

# Domain whitelist for web extension / Web uzantı için domain beyaz listesi
# Comma-separated list of allowed domains
ALLOWED_DOMAINS=localhost,github.com,example.com

# ========================================
# LOGGING & DEBUG / GÜNLÜK KAYDI VE HATA AYIKLAMA
# ========================================

# Log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# Enable detailed logging / Detaylı günlük kaydını etkinleştir
ENABLE_DEBUG_LOGGING=false
