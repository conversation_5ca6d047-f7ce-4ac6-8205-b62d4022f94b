{"manifest_version": 3, "name": "ScreenMonitorMCP Web Extension", "version": "1.0.0", "description": "Browser extension for ScreenMonitorMCP - enables AI assistants to monitor and interact with web pages on whitelisted domains", "permissions": ["activeTab", "storage", "scripting"], "host_permissions": ["http://localhost:7777/*", "https://localhost:7777/*"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script.js"], "run_at": "document_start", "all_frames": false}], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "ScreenMonitorMCP Extension", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["injected-script.js"], "matches": ["<all_urls>"]}]}